# Product Requirements Document (PRD): Banana Checklist

**Version:** 1.0\
**Date:** July 6, 2025\
**Author:** Grok 3 (assisted by <PERSON>A<PERSON>)\
**Project Name:** Banana Checklist\
**Target Platforms:** Web, iOS, Android\
**Tech Stack:** TypeScript, PixiJS, SvelteKit

---

## 1. Product Overview

### 1.1 Purpose

Banana Checklist is a hybrid productivity and gaming app that makes task management fun and engaging through a banana-themed incremental game. Users manage to-do lists and goals while navigating a pixel-art monkey through a retro-inspired jungle to collect bananas, complete quests, and unlock advanced app features. The app appeals to a broad audience (students, professionals, casual users) by combining intuitive task tracking with nostalgic 80s/90s-style gameplay, incentivizing productivity through rewards.

### 1.2 Objectives

- **Core Value:** Provide an intuitive task and goal tracker that encourages consistent use through gamification.
- **Engagement:** Blend incremental game mechanics with productivity, rewarding task completion with in-game bananas that unlock app features.
- **Accessibility:** Offer a freemium model where all features are unlockable through gameplay, with a premium subscription for instant access and cosmetic perks.
- **Fun Factor:** Emulate 80s/90s retro games (e.g., Donkey Kong, Zelda, Tetris) using PixiJS for a nostalgic, engaging experience.
- **Privacy:** Prioritize local storage for task data, with optional cloud syncing for user flexibility.

### 1.3 Target Audience

- **Primary Users:** General users aged 16–40 who need task management (e.g., students tracking assignments, professionals managing work, individuals pursuing personal goals like fitness).
- **Secondary Users:** Gamers who enjoy incremental and retro games, drawn to the app’s playful banana theme and nostalgic mechanics.
- **Technical Level:** No technical knowledge required; markdown is replaced with a simple to-do list interface to maximize accessibility.

---

## 2. Features

### 2.1 Core App Features (Task and Goal Tracker)

- **To-Do List:**
  - Add, edit, and delete tasks with fields for title, description, due date, and priority.
  - Mark tasks as complete with a checkbox, triggering in-game banana rewards.
  - Data is synced in real-time to a central API server and database, enabling persistent task and game state across all active clients.
- **Goal Tracking:**
  - Create long-term goals (e.g., “Run a marathon”) with milestones (e.g., “Run 5km this week”).
  - Track progress with completion percentages and streaks.
- **Categories and Tags:**
  - Organize tasks by categories (e.g., Work, Personal, Habits) and custom tags.
- **Act Busy Button:**
  - Generates a random Mad Libs-style task (e.g., “Write the report by Friday”) and auto-completes it, earning bananas without manual task entry.
  - Uses a simple algorithm to fill templates (e.g., “[Verb] the [Noun] by [Time]”) with predefined word lists.
- **Progressive Unlocks:**
  - Free users unlock advanced features through gameplay (e.g., 100 bananas for categories, 500 for habit tracking).
  - **Features include:**
    - **Basic Tier:** Simple to-do list with checkboxes.
    - **Mid Tier:** Categories, due dates, priority tags, basic analytics (e.g., tasks completed per week).
    - **Advanced Tier:** Habit tracking (streaks), progress charts, export options (CSV, PDF).
    - **Premium Tier:** Collaboration (shared task lists), calendar sync, custom themes.
- **Premium Subscription:**
  - Unlocks all app features instantly, plus exclusive game cosmetics (e.g., golden monkey skin, banana crown).

### 2.2 Game Features

- **Setting:** A 16-bit pixel-art jungle where a customizable monkey navigates to collect bananas, complete quests, and interact with task trees.

- **Core Mechanics:**

  - **Banana Harvesting:** Collect bananas by navigating to trees or completing tasks. Bananas are the in-game currency for upgrades.
  - **Task Trees:** Interact with trees to add/complete tasks, earning bananas (e.g., small task = 5 bananas, goal milestone = 50 bananas).
  - **Act Busy Minigame:** A Mad Libs-style text prompt generates a task (e.g., “Organize desk by tomorrow”) and auto-completes it for 3–5 bananas.

- **Retro-Inspired Mechanics (using PixiJS):**

  1. **Platformer Navigation (Donkey Kong):**
     - Jump, climb, and swing through platforms and vines to reach Task Groves.
     - Controls: Arrow keys/WASD for movement, spacebar for jump, hold key for vine-swinging.
     - Rewards: Reach a grove to unlock task entry; complete tasks for bonus bananas.
  2. **Exploration (Zelda):**
     - Top-down jungle map with hidden groves, task trees, and NPC monkeys offering quests (e.g., “Complete 5 tasks today”).
     - Rewards: Discover new areas or secrets (e.g., banana caches) for extra bananas.
  3. **Banana Sorting Minigame (Tetris):**
     - Sort falling bananas into baskets (Work, Personal, Habits) before they stack too high.
     - Controls: Arrow keys/touch to move/rotate bananas.
     - Rewards: Correct sorting auto-completes tasks or earns bonus bananas.
  4. **Banana Defense (Space Invaders):**
     - Shoot banana peels to repel “Banana Bandits” stealing your stash.
     - Controls: Left/right arrows to move, spacebar to shoot.
     - Rewards: Winning protects bananas and boosts task rewards; losing deducts a few bananas.

- **Upgrades:**

  - Spend bananas on game upgrades (e.g., “Faster Monkey: +10% movement speed,” “Banana Bots: Auto-harvest 1 banana/minute”).
  - Unlock app features (e.g., 1,000 bananas for habit tracking).

- **Quests and Achievements:**

  - Quests: “Complete 3 tasks today” or “Maintain a 7-day habit streak” for bonus bananas.
  - Achievements: “Harvest 1,000 Bananas” or “Complete 100 Tasks” for cosmetic rewards (e.g., monkey hats).

- **Cosmetics:** Customizable monkey skins, hats, or jungle themes (e.g., neon jungle), with premium-exclusive options.

### 2.3 Freemium Model

- **Free Tier:**
  - Access to basic task list and core game mechanics.
  - Unlock advanced app features (e.g., analytics, collaboration) by earning bananas through gameplay.
  - Act Busy button available with a cap (e.g., 5 uses/day) to encourage real task use.
- **Premium Tier:**
  - Instant access to all app features and exclusive cosmetics.
  - Removes Act Busy cap and adds premium quests (e.g., “Earn double bananas for tasks today”).
  - Pricing and subscription details at [https://x.ai/grok](https://x.ai/grok).

### 2.4 User Experience

- **Onboarding:**
  - Tutorial: Guide users through adding a task, navigating the jungle, and harvesting bananas.
  - Start with a simple grove and basic task list, introducing mechanics progressively.
- **Game-App Loop:**
  - Add a task → Navigate to a Task Grove → Complete task → Earn bananas → Spend on upgrades/features.
  - Act Busy button generates quick tasks for casual play, ensuring no productivity barrier.
- **Visuals:**
  - 16-bit pixel-art jungle with banana trees, vines, and monkey animations.
  - UI: Banana-themed buttons (e.g., peel-shaped), task lists as banana bunches, particle effects (e.g., banana sparkles) for rewards.
  - Optional retro filters (e.g., CRT effect via PixiJS’s CRTFilter) for nostalgia, toggleable for accessibility.
- **Sound:**
  - Chiptune background music, monkey chirps, and banana-plucking sounds (optional, toggleable).
- **Feedback:**
  - Animations for task completion (e.g., golden banana burst) and game actions (e.g., vine-swinging).
  - Progress bars for banana collection and feature unlocks.

## 3. Technical Requirements

### 3.1 Tech Stack

- **WebSockets Integration:** Real-time updates and state synchronization between clients and the central API server are handled via WebSockets. This ensures low-latency task/game state syncing across active sessions.

- **Frontend:** SvelteKit for the app UI, PixiJS for the game rendering, TypeScript for type safety.

- **Storage:** Centralized API server and database architecture for real-time syncing of task and game data across clients; local caching only for offline access or performance optimization.

- **Assets:** Pixel-art sprites created in Aseprite, loaded as sprite sheets via PixiJS’s TexturePacker.

- **Deployment:** Web app hosted via SvelteKit’s static adapter; mobile apps via Capacitor for iOS/Android.

### 3.2 PixiJS Game Implementation

- **Core Setup:**

  - Initialize PixiJS Application with a 800x600 canvas (scalable for mobile).
  - Use Sprite for monkey, trees, and UI elements; AnimatedSprite for animations (e.g., monkey jumping).
  - Implement game loop with ticker for smooth updates at 60 FPS.

- **Mechanics:**

  - Platformer: Use bounding box collision detection for platforms/vines. Implement gravity and jump mechanics via delta updates.
  - Exploration: Load tilemap JSON (from Tiled) for jungle layout. Use TilingSprite for repeating backgrounds and Container for interactive objects.
  - Sorting Minigame: Manage falling bananas with Sprite arrays, checking collisions with baskets via hit areas.
  - Defense Minigame: Use Sprite for enemies and projectiles, with wave-based spawning and collision detection.
  - Act Busy: Render Mad Libs UI with Text and Graphics, animating text reveal with ticker.

- **Effects:**

  - Use ParticleContainer for banana sparkles, task completion bursts, or bandit explosions.
  - Apply CRTFilter or PixelateFilter for retro aesthetics, toggleable in settings.

- **Optimization:**

  - Use sprite pooling for particles/enemies (inspired by your cellular automata project).
  - Optimize textures for WebGL, ensuring 60 FPS on low-end devices.

### 3.3 SvelteKit App Implementation

- **WebSocket Sync:**

  - Establish persistent WS connection to receive task updates, banana count changes, and real-time collaboration events.
  - Fall back to polling or manual sync on connection failure.

- **Task UI:**

  - Svelte components for task input, list display, and category filters.
  - Reactive state for task updates, synced with local storage.

- **Integration with Game:**

  - Svelte stores to share banana count and unlocked features between game and app.
  - Trigger PixiJS animations (e.g., banana burst) on task completion via event listeners.

- **Act Busy:**

  - Svelte component with Mad Libs logic (random word selection from arrays).
  - Auto-adds task to storage and marks complete, updating banana count.

- **Freemium Logic:**

  - Store user tier (free/premium) in local storage or server (if synced).
  - Conditional rendering for locked features, with banana purchase prompts.

### 3.4 Performance Goals

- **Load Time:** App loads in <2 seconds on modern browsers/devices.
- **Frame Rate:** Game maintains 60 FPS on web and mobile (tested on mid-range devices).
- **Storage:** Client uses local cache only for performance optimization; all data is persisted via API to a central DB.
- **Accessibility:** Keyboard navigation, high-contrast mode, toggleable animations/sounds.

### 3.5 Security and Privacy

- **Authentication:** Anonymous accounts by default, with optional signup using magic email links (via SendGrid) to enable cloud save and cross-device sync.
- **Rate Limiting:** All user-facing API endpoints are rate-limited to prevent abuse, including Act Busy requests.
- **Data Safety:** No personal data collection beyond tasks; premium subscription handled via xAI’s secure payment system.

## 3.6 Backend API Architecture

- **Overview:** The backend is a RESTful API built to handle all task and game state management. WebSockets are used for real-time synchronization between active clients.

- **Authentication:**

  - Anonymous user by default.
  - Optional signup via magic email links using SendGrid.
  - User sessions are short-lived tokens stored client-side.

- **Core Endpoints:**

  - `POST /auth/signup` → Trigger email magic link.
  - `POST /auth/verify` → Validate magic link token.
  - `GET /tasks` → Fetch all tasks.
  - `POST /tasks` → Create new task.
  - `PUT /tasks/:id` → Update task.
  - `DELETE /tasks/:id` → Delete task.
  - `POST /tasks/:id/complete` → Mark task as complete and award bananas.
  - `GET /user/state` → Return full user state (bananas, tasks, upgrades).
  - `POST /act-busy` → Generate and complete a Mad Libs task.
  - `GET /store` → Fetch available upgrades/cosmetics.
  - `POST /store/purchase` → Spend bananas on feature unlocks.

- **WebSocket Events:**

  - `connect` → Authenticate and join user-specific channel.
  - `task-updated` → Notify of task state change.
  - `banana-count-updated` → Sync banana balance across tabs/devices.
  - `upgrade-unlocked` → Push unlocked features.

- **Rate Limiting:**

  - All endpoints (especially `/act-busy`, `/store/purchase`) are subject to rate limits to prevent abuse.

- **Tech Stack (Server-side):**

  - Node.js or Bun runtime.
  - REST API via Fastify or Express.
  - WebSocket server using `ws` or socket.io.
  - PostgreSQL for persistent state.
  - Redis optional for real-time pub/sub.

- **Security:**

  - JWT tokens for authentication.
  - CSRF protection where applicable.
  - Input validation for all routes.

## 4. User Flow

1. **Onboarding:**
   - User launches app, sees pixel-art jungle and monkey.
   - Tutorial: Add a task, navigate to a Task Grove, complete task for bananas.
2. **Daily Use:**
   - Add tasks/goals via Svelte UI or use Act Busy button for quick bananas.
   - Navigate monkey to collect bananas or complete quests (platformer/exploration).
   - Play minigames (sorting, defense) for bonus rewards.
3. **Progression:**
   - Spend bananas to unlock game upgrades (e.g., Banana Bots) and app features (e.g., habit tracking).
   - Free users grind bananas; premium users access all features instantly.
4. **Feedback:**
   - Task completion triggers animations/sounds; banana count updates in real-time.
   - Achievements/quests notify users of milestones (e.g., “100 Tasks Completed!”).

## 5. Milestones and Deliverables

- **Phase 1: MVP (3 months):**
  - Basic task list (add, edit, delete, complete).
  - Core game: Platformer navigation, banana harvesting, Task Groves.
  - Act Busy button with Mad Libs minigame.
  - Local storage with FileSystem API.
  - Pixel-art assets (monkey, jungle, bananas).
- **Phase 2: Enhanced Features (2 months):**
  - Exploration mode with tilemap and quests.
  - Sorting and defense minigames.
  - App features: Categories, habit tracking, basic analytics.
  - Freemium logic with banana-based unlocks.
- **Phase 3: Polish and Launch (1 month):**
  - Premium subscription integration (via xAI).
  - Optional Syncthing sync.
  - Advanced app features: Collaboration, calendar sync, export.
  - Performance optimization and testing.
- **Phase 4: Post-Launch (Ongoing):**
  - Add new cosmetics, quests, and jungle areas.
  - User feedback-driven updates (e.g., new minigames).

## 6. Success Metrics

- **Engagement:** Average 5 tasks completed per user daily; 10 minutes daily game time.
- **Retention:** 70% of users return within 7 days.
- **Freemium Conversion:** 5% of free users upgrade to premium within 30 days.
- **Performance:** 60 FPS on 90% of target devices; <2-second load time.
- **User Satisfaction:** 4.5/5 average rating on app stores; positive X feedback.

## 7. Risks and Mitigations

- **Risk:** Task tracker too simple compared to competitors (e.g., Todoist).
  - **Mitigation:** Emphasize gamification to differentiate; add advanced features via unlocks.
- **Risk:** Game mechanics feel repetitive.
  - **Mitigation:** Vary mechanics (platformer, exploration, minigames) and add frequent quests/achievements.
- **Risk:** Act Busy button overused, reducing productivity value.
  - **Mitigation:** Cap daily uses for free users; make real tasks yield higher bananas.
- **Risk:** Performance issues on low-end devices.
  - **Mitigation:** Optimize PixiJS with sprite pooling; test on mid-range devices.

## 8. Future Considerations

- **Server Architecture:** RESTful API design with WebSocket channels for real-time updates.

  - Example endpoints: `POST /tasks`, `GET /user/:id/state`, `WS /user/:id/updates`
  - All real-time data (e.g. bananas, tasks) is centrally stored and synchronized.

- Multiplayer: Add banana trading or shared task lists for teams.

- AI Features: Integrate lightweight RAG (from your PKM ideas) for task suggestions, unlocked at high banana tiers.

- New Minigames: Introduce additional retro-inspired mechanics (e.g., Pac-Man-style banana maze).

- Cross-Platform: Expand to desktop apps using Tauri (planned in a later milestone) for lightweight, native performance and offline use.

## 9. Sample Code Snippet

```typescript
import * as PIXI from 'pixi.js';

const app = new PIXI.Application({ width: 800, height: 600, backgroundColor: 0x228B22 });
document.body.appendChild(app.view);

// Load assets
PIXI.Loader.shared.add('monkey', 'monkey.png').add('banana', 'banana.png').load(setup);

function setup() {
  // Monkey sprite
  const monkey = new PIXI.Sprite(PIXI.Loader.shared.resources['monkey'].texture);
  monkey.position.set(100, 500);
  monkey.vx = 0;
  monkey.vy = 0;
  app.stage.addChild(monkey);

  // Banana sprite
  const banana = new PIXI.Sprite(PIXI.Loader.shared.resources['banana'].texture);
  banana.position.set(400, 300);
  app.stage.addChild(banana);

  // Input handling
  window.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowLeft') monkey.vx = -5;
    if (

```
